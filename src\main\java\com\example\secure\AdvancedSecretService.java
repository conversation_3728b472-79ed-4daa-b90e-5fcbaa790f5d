package com.example.secure;

import com.example.service.SecureLogicCaller;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 高级敏感服务类 - 演示依赖注入和Spring集成
 *
 * 这个类将被加密，但仍然可以：
 * 1. 被Spring自动扫描和管理
 * 2. 注入其他Spring Bean
 * 3. 被其他组件注入和使用
 * 4. 支持所有Spring特性
 */
@Slf4j
@Service
public class AdvancedSecretService {
    
    @Autowired
    private SecretLogic secretLogic;
    
    @Autowired
    private SecureLogicCaller secureLogicCaller;
    
    /**
     * 执行高级敏感操作
     */
    public String executeAdvancedOperation(String input) {
        log.info("=== Advanced Secret Service ===");
        log.info("Input: {}", input);
        
        // 调用其他加密服务
        String config = secretLogic.getSecretConfig();
        String calculation = secretLogic.performSecretCalculation();
        String processed = secretLogic.processSensitiveData(input);
        
        // 组合结果
        StringBuilder result = new StringBuilder();
        result.append("Advanced Operation Result:\n");
        result.append("- Config: ").append(config).append("\n");
        result.append("- Calculation: ").append(calculation).append("\n");
        result.append("- Processed: ").append(processed).append("\n");
        result.append("- Timestamp: ").append(System.currentTimeMillis());
        
        String finalResult = result.toString();
        log.info("Advanced operation result: {}", finalResult);

        return finalResult;
    }
    
    /**
     * 验证复杂业务规则
     */
    public boolean validateComplexBusinessRule(String userId, String data, String operation) {
        log.info("Validating complex business rule...");
        
        // 使用注入的服务进行权限验证
        boolean hasPermission = secretLogic.validateUserPermission(userId, operation);
        
        if (!hasPermission) {
            log.warn("Permission denied for user: {}", userId);
            return false;
        }
        
        // 复杂的业务规则验证
        boolean isValidData = data != null && data.length() > 0;
        boolean isValidOperation = operation != null && !operation.trim().isEmpty();
        boolean isValidUser = userId != null && userId.length() >= 3;
        
        boolean result = isValidData && isValidOperation && isValidUser;
        
        log.info("Complex business rule validation result: {}", result);
        return result;
    }
    
    /**
     * 获取服务状态信息
     */
    public String getServiceStatus() {
        StringBuilder status = new StringBuilder();
        status.append("Advanced Secret Service Status:\n");
        status.append("- Service Active: true\n");
        status.append("- SecretLogic Injected: ").append(secretLogic != null).append("\n");
        status.append("- SecureLogicCaller Injected: ").append(secureLogicCaller != null).append("\n");
        status.append("- ClassLoader: ").append(this.getClass().getClassLoader().getClass().getSimpleName()).append("\n");
        status.append("- Package: ").append(this.getClass().getPackage().getName()).append("\n");
        
        return status.toString();
    }
    
    /**
     * 执行批量敏感操作
     */
    public String[] executeBatchOperations(String[] inputs) {
        if (inputs == null || inputs.length == 0) {
            return new String[]{"No inputs provided"};
        }
        
        String[] results = new String[inputs.length];
        
        for (int i = 0; i < inputs.length; i++) {
            try {
                results[i] = executeAdvancedOperation(inputs[i]);
            } catch (Exception e) {
                results[i] = "Error processing input " + i + ": " + e.getMessage();
            }
        }
        
        return results;
    }
}

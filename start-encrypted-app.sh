#!/bin/bash

echo "========================================"
echo "Spring Boot 加密应用启动脚本"
echo "========================================"

# 执行打包和加密文件准备
echo "0. 准备应用..."
# 清理旧的jar包和加密文件
echo "清理旧文件..."
rm -rf target
rm -rf encrypted

# 执行Maven打包
echo "使用Maven构建项目..."
mvn clean package -DskipTests
if [ $? -ne 0 ]; then
    echo "❌ 错误: Maven构建失败"
    exit 1
fi

# 复制加密文件
echo "复制加密文件..."
rm -rf encrypted
cp -r target/encrypted encrypted/
if [ $? -ne 0 ]; then
    echo "❌ 错误: 复制加密文件失败"
    exit 1
fi

# 检查必要文件是否存在
echo "1. 检查文件完整性..."

if [ ! -f "target/springboot-encrypted-demo.jar" ]; then
    echo "❌ 错误: 主JAR包不存在 - target/springboot-encrypted-demo.jar"
    echo "请先执行: mvn clean package"
    exit 1
fi
echo "✅ 主JAR包存在: target/springboot-encrypted-demo.jar"

if [ ! -d "encrypted/com/example/secure" ]; then
    echo "❌ 错误: 加密文件目录不存在 - encrypted/com/example/secure"
    echo "请先执行: cp -r target/encrypted encrypted/"
    exit 1
fi
echo "✅ 加密文件目录存在: encrypted/com/example/secure"

# 统计加密文件数量
encrypted_count=$(find encrypted/com/example/secure -name "*.class" | wc -l)
echo "✅ 发现 $encrypted_count 个加密类文件"

# 验证加密文件是否真的加密
echo "验证加密文件状态..."
for file in encrypted/com/example/secure/*.class; do
    if file "$file" | grep -q "Java class"; then
        echo "❌ 警告: $(basename "$file") 可能未加密 (仍为Java class格式)"
        exit 1
    else
        echo "✅ $(basename "$file") 已正确加密"
    fi
done

echo ""
echo "2. 文件结构概览:"
echo "├── target/springboot-encrypted-demo.jar     (主应用JAR - 不含敏感类)"
echo "├── encrypted/com/example/secure/            (加密的敏感类文件)"
echo "│   ├── SecretLogic.class                    (AES加密)"
echo "│   ├── AdvancedSecretService.class          (AES加密)"
echo "│   └── TransactionalSecretService.class     (AES加密)"
echo "└── target/dependency/                       (Spring Boot依赖JAR包)"

echo ""
echo "3. 启动模式选择:"
echo "[1] 生产模式 (使用默认密钥)"
echo "[2] 调试模式 (详细日志输出)"
echo "[3] 自定义密钥模式"
read -p "请选择启动模式 (1-3): " mode

echo ""
echo "4. 启动应用..."

case $mode in
    1)
        echo "🚀 启动模式: 生产模式 (使用默认密钥)"
        echo "命令: java -jar target/springboot-encrypted-demo.jar"
        java -jar target/springboot-encrypted-demo.jar
        ;;
    2)
        echo "🚀 启动模式: 调试模式 (详细日志输出)"
        echo "命令: java -Dencrypt.debug=true -jar target/springboot-encrypted-demo.jar"
        java -Dencrypt.debug=true -jar target/springboot-encrypted-demo.jar
        ;;
    3)
        read -p "请输入解密密钥: " custom_key
        echo "🚀 启动模式: 自定义密钥模式"
        echo "命令: java -Ddecrypt.key=$custom_key -jar target/springboot-encrypted-demo.jar"
        java -Ddecrypt.key="$custom_key" -jar target/springboot-encrypted-demo.jar
        ;;
    *)
        echo "❌ 无效选择，使用默认生产模式"
        java -jar target/springboot-encrypted-demo.jar
        ;;
esac

echo ""
echo "========================================"
echo "应用已退出"
echo "========================================"

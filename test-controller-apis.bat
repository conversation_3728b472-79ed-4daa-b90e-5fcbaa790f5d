@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM TestController API 测试脚本 (Windows版本)
REM 用于测试所有的接口功能

set BASE_URL=http://localhost:8080

echo === TestController API 测试开始 ===
echo 基础URL: %BASE_URL%
echo.

REM 测试函数
:test_api
set api_name=%~1
set api_url=%~2
echo 测试: %api_name%
echo URL: %api_url%
echo 方法: GET

curl -s "%api_url%" > temp_response.txt
if %errorlevel% equ 0 (
    echo ✓ 成功
    echo 响应:
    type temp_response.txt
) else (
    echo ✗ 失败
)
echo ----------------------------------------
del temp_response.txt 2>nul
goto :eof

REM 1. 基础功能测试
echo === 1. 基础功能测试 ===

call :test_api "旧版本兼容接口" "%BASE_URL%/run-secure"

call :test_api "直接注入加密类" "%BASE_URL%/run-secure-direct"

call :test_api "高级加密服务(默认参数)" "%BASE_URL%/run-advanced"

call :test_api "高级加密服务(自定义参数)" "%BASE_URL%/run-advanced?input=custom-test-data"

REM 2. 权限验证测试
echo.
echo === 2. 权限验证测试 ===

call :test_api "权限验证(默认参数)" "%BASE_URL%/validate-permission"

call :test_api "权限验证(自定义用户)" "%BASE_URL%/validate-permission?userId=admin&operation=WRITE_DATA"

call :test_api "复杂业务规则验证(默认)" "%BASE_URL%/validate-business-rule"

call :test_api "复杂业务规则验证(自定义)" "%BASE_URL%/validate-business-rule?userId=testuser&data=sensitive-data&operation=DELETE_SENSITIVE"

REM 3. 事务功能测试
echo.
echo === 3. 事务功能测试 ===

call :test_api "基础事务测试" "%BASE_URL%/test-transaction"

call :test_api "自定义事务测试" "%BASE_URL%/test-transaction?data=my-transaction-data"

call :test_api "事务回滚测试(成功)" "%BASE_URL%/test-transaction-rollback?shouldFail=false"

call :test_api "事务回滚测试(失败)" "%BASE_URL%/test-transaction-rollback?shouldFail=true"

call :test_api "嵌套事务测试" "%BASE_URL%/test-nested-transaction"

call :test_api "只读事务测试" "%BASE_URL%/test-readonly-transaction"

call :test_api "只读事务测试(自定义查询)" "%BASE_URL%/test-readonly-transaction?query=custom-query-data"

REM 4. 数据库功能测试
echo.
echo === 4. 数据库功能测试 ===

call :test_api "数据库查询测试" "%BASE_URL%/test-database-query"

call :test_api "数据库插入测试(默认)" "%BASE_URL%/test-database-insert"

call :test_api "数据库插入测试(自定义)" "%BASE_URL%/test-database-insert?data=custom-db-data"

REM 5. 监控和状态测试
echo.
echo === 5. 监控和状态测试 ===

call :test_api "获取性能统计" "%BASE_URL%/performance-stats"

call :test_api "重置性能统计" "%BASE_URL%/reset-performance-stats"

call :test_api "获取服务状态" "%BASE_URL%/service-status"

echo.
echo === TestController API 测试完成 ===
pause

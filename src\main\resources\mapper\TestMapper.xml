<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.secure.dao.TestDao">

    <!-- 结果映射 -->
    <resultMap id="TestEntityResultMap" type="com.example.secure.entity.TestEntity">
        <id column="id" property="id"/>
        <result column="contract_id" property="contractId"/>
        <result column="version" property="version"/>
        <result column="token_id" property="tokenId"/>
        <result column="user_address" property="userAddress"/>
        <result column="user_name" property="userName"/>
        <result column="create_time" property="createTime"/>
        <result column="deleted" property="deleted"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, contract_id, version, token_id, user_address, user_name, 
        create_time, deleted, update_time
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Integer" resultMap="TestEntityResultMap">
        SELECT 
            <include refid="Base_Column_List"/>
        FROM test 
        WHERE id = #{id} AND deleted = 0
    </select>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="TestEntityResultMap">
        SELECT 
            <include refid="Base_Column_List"/>
        FROM test 
        WHERE deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据用户地址查询 -->
    <select id="selectByUserAddress" parameterType="java.lang.String" resultMap="TestEntityResultMap">
        SELECT 
            <include refid="Base_Column_List"/>
        FROM test 
        WHERE user_address = #{userAddress} AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.example.secure.entity.TestEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO test (
            contract_id, version, token_id, user_address, user_name, 
            create_time, deleted, update_time
        ) VALUES (
            #{contractId}, #{version}, #{tokenId}, #{userAddress}, #{userName},
            #{createTime}, #{deleted}, #{updateTime}
        )
    </insert>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.example.secure.entity.TestEntity">
        UPDATE test 
        SET 
            contract_id = #{contractId},
            version = #{version},
            token_id = #{tokenId},
            user_address = #{userAddress},
            user_name = #{userName},
            update_time = #{updateTime}
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 逻辑删除 -->
    <update id="deleteById" parameterType="java.lang.Integer">
        UPDATE test 
        SET deleted = 1, update_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>

</mapper>

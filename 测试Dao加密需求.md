# 需求

1. 修改resources目录的application.properties配置文件为yaml格式，同时补充mysql的连接，连接库crypto_nijigen
2. 在secure增加一个Dao类和xml文件，我要测试mybatis在加密时的情况。只要提供一个select和insert就行，操作表test，表结构如下
```sql
CREATE TABLE `test` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `contract_id` int(11) DEFAULT NULL,
  `version` int(11) DEFAULT NULL,
  `token_id` int(11) DEFAULT NULL,
  `user_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `deleted` int(11) DEFAULT '0',
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


```
3. TransactionalSecretService.java操作这个dao中的
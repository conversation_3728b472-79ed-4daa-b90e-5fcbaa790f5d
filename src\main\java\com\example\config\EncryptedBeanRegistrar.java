package com.example.config;

import com.example.secureloader.EncryptedClassLoader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.stream.Stream;

/**
 * 加密Bean注册器
 *
 * 手动注册加密的Service类到Spring容器中
 * 这样可以避免Spring扫描加密文件时出现的解析错误
 */
@Slf4j
@Component
public class EncryptedBeanRegistrar implements BeanDefinitionRegistryPostProcessor {
    
    private static final String ENCRYPTED_PACKAGE = "com.example.secure";
    
    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        log.info("🔧 [REGISTRAR] Starting encrypted bean registration...");

        try {
            // 创建自定义ClassLoader
            URL[] urls = {};
            ClassLoader currentClassLoader = this.getClass().getClassLoader();
            EncryptedClassLoader encryptedLoader = new EncryptedClassLoader(urls, currentClassLoader);

            // 手动注册加密的Service类
            registerEncryptedBean(registry, encryptedLoader, "com.example.secure.SecretLogic", "secretLogic");
            registerEncryptedBean(registry, encryptedLoader, "com.example.secure.AdvancedSecretService", "advancedSecretService");
            registerEncryptedBean(registry, encryptedLoader, "com.example.secure.TransactionalSecretService", "transactionalSecretService");

            log.info("✅ [REGISTRAR] Encrypted bean registration completed");

        } catch (Exception e) {
            log.error("❌ [REGISTRAR] Failed to register encrypted beans: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        // 不需要处理
    }
    
    /**
     * 注册单个加密Bean
     */
    private void registerEncryptedBean(BeanDefinitionRegistry registry, EncryptedClassLoader classLoader, 
                                     String className, String beanName) {
        try {
            log.info("🔐 [REGISTRAR] Registering encrypted bean: {}", className);
            
            // 使用自定义ClassLoader加载加密的类
            Class<?> encryptedClass = classLoader.loadClass(className);
            
            // 创建BeanDefinition
            BeanDefinition beanDefinition = BeanDefinitionBuilder
                    .genericBeanDefinition(encryptedClass)
                    .setScope(BeanDefinition.SCOPE_SINGLETON)
                    .getBeanDefinition();
            
            // 注册到Spring容器
            registry.registerBeanDefinition(beanName, beanDefinition);
            
            log.info("✅ [REGISTRAR] Successfully registered: {} -> {}", beanName, className);

        } catch (Exception e) {
            log.error("❌ [REGISTRAR] Failed to register bean {}: {}", beanName, e.getMessage(), e);
        }
    }
}


package com.example.controller;

import com.example.service.SecureLogicCaller;
import com.example.monitoring.PerformanceMonitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试控制器 - 验证加密类的Spring集成
 */
@RestController
public class TestController {

    @Autowired
    private SecureLogicCaller caller;

    // 通过ApplicationContext动态获取加密的Service类，避免编译时依赖
    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private PerformanceMonitor performanceMonitor;

    /**
     * 辅助方法：动态调用加密服务的方法
     */
    private Object callEncryptedService(String beanName, String methodName, Class<?>[] paramTypes, Object... args) {
        try {
            Object service = applicationContext.getBean(beanName);
            return service.getClass().getMethod(methodName, paramTypes).invoke(service, args);
        } catch (Exception e) {
            throw new RuntimeException("Error calling encrypted service: " + e.getMessage(), e);
        }
    }

    /**
     * 旧版本兼容接口 - 手动加载方式
     */
    @GetMapping("/run-secure")
    public String run() {
        caller.runSecureLogic();
        return "Secure logic executed via manual loading";
    }

    /**
     * 新版本接口 - 直接使用Spring注入的加密类
     */
    @GetMapping("/run-secure-direct")
    public String runDirect() {
        System.out.println("=== Testing Direct Injection of Encrypted Classes ===");

        try {
            // 使用辅助方法调用加密服务
            callEncryptedService("secretLogic", "run", new Class<?>[0]);
            Object result = callEncryptedService("secretLogic", "performSecretCalculation", new Class<?>[0]);

            return "Direct encrypted service call successful: " + result;
        } catch (Exception e) {
            return "Error calling encrypted service: " + e.getMessage();
        }
    }

    /**
     * 测试高级加密服务
     */
    @GetMapping("/run-advanced")
    public String runAdvanced(@RequestParam(defaultValue = "test-data") String input) {
        System.out.println("=== Testing Advanced Encrypted Service ===");

        try {
            Object result = callEncryptedService("advancedSecretService", "executeAdvancedOperation", new Class<?>[]{String.class}, input);
            return "Advanced encrypted service result: " + result;
        } catch (Exception e) {
            return "Error calling advanced encrypted service: " + e.getMessage();
        }
    }

    /**
     * 测试权限验证
     */
    @GetMapping("/validate-permission")
    public String validatePermission(
            @RequestParam(defaultValue = "testuser") String userId,
            @RequestParam(defaultValue = "READ_DATA") String operation) {

        try {
            Object hasPermission = callEncryptedService("secretLogic", "validateUserPermission", new Class<?>[]{String.class, String.class}, userId, operation);
            return String.format("User %s permission for %s: %s", userId, operation, hasPermission);
        } catch (Exception e) {
            return "Error validating permission: " + e.getMessage();
        }
    }

    /**
     * 测试复杂业务规则
     */
    @GetMapping("/validate-business-rule")
    public String validateBusinessRule(
            @RequestParam(defaultValue = "admin") String userId,
            @RequestParam(defaultValue = "sample-data") String data,
            @RequestParam(defaultValue = "READ_SENSITIVE") String operation) {

        try {
            Object isValid = callEncryptedService("advancedSecretService", "validateComplexBusinessRule",
                new Class<?>[]{String.class, String.class, String.class}, userId, data, operation);
            return String.format("Business rule validation result: %s", isValid);
        } catch (Exception e) {
            return "Error validating business rule: " + e.getMessage();
        }
    }

    /**
     * 测试事务功能
     */
    @GetMapping("/test-transaction")
    public String testTransaction(@RequestParam(defaultValue = "transaction-test-data") String data) {
        try {
            Object result = callEncryptedService("transactionalSecretService", "executeTransactionalOperation", new Class<?>[]{String.class}, data);
            return "Transaction test successful: " + result;
        } catch (Exception e) {
            return "Transaction test failed: " + e.getMessage();
        }
    }

    /**
     * 测试事务回滚
     */
    @GetMapping("/test-transaction-rollback")
    public String testTransactionRollback(
            @RequestParam(defaultValue = "rollback-test-data") String data,
            @RequestParam(defaultValue = "true") boolean shouldFail) {
        try {
            Object result = callEncryptedService("transactionalSecretService", "executeTransactionalOperationWithRollback",
                new Class<?>[]{String.class, boolean.class}, data, shouldFail);
            return "Transaction rollback test result: " + result;
        } catch (Exception e) {
            return "Transaction rollback test (expected failure): " + e.getMessage();
        }
    }

    /**
     * 测试嵌套事务
     */
    @GetMapping("/test-nested-transaction")
    public String testNestedTransaction(@RequestParam(defaultValue = "nested-test-data") String data) {
        try {
            Object result = callEncryptedService("transactionalSecretService", "executeNestedTransactionalOperation", new Class<?>[]{String.class}, data);
            return "Nested transaction test successful: " + result;
        } catch (Exception e) {
            return "Nested transaction test failed: " + e.getMessage();
        }
    }

    /**
     * 测试只读事务
     */
    @GetMapping("/test-readonly-transaction")
    public String testReadOnlyTransaction(@RequestParam(defaultValue = "readonly-query") String query) {
        try {
            Object result = callEncryptedService("transactionalSecretService", "executeReadOnlyOperation", new Class<?>[]{String.class}, query);
            return "Read-only transaction test successful: " + result;
        } catch (Exception e) {
            return "Read-only transaction test failed: " + e.getMessage();
        }
    }

    /**
     * 获取性能统计
     */
    @GetMapping("/performance-stats")
    public String getPerformanceStats() {
        return performanceMonitor.getDetailedStats().replace("\n", "<br>");
    }

    /**
     * 重置性能统计
     */
    @GetMapping("/reset-performance-stats")
    public String resetPerformanceStats() {
        performanceMonitor.reset();
        return "Performance statistics reset successfully";
    }

    /**
     * 获取服务状态
     */
    @GetMapping("/service-status")
    public String getServiceStatus() {
        StringBuilder status = new StringBuilder();
        status.append("=== Service Status ===\n");
        status.append("SecretLogic available: ").append(applicationContext.containsBean("secretLogic")).append("\n");
        status.append("AdvancedSecretService available: ").append(applicationContext.containsBean("advancedSecretService")).append("\n");
        status.append("TransactionalSecretService available: ").append(applicationContext.containsBean("transactionalSecretService")).append("\n");
        status.append("SecureLogicCaller injected: ").append(caller != null).append("\n");
        status.append("PerformanceMonitor injected: ").append(performanceMonitor != null).append("\n");
        status.append("\n");
        try {
            Object serviceStatus = callEncryptedService("advancedSecretService", "getServiceStatus", new Class<?>[0]);
            status.append(serviceStatus);
        } catch (Exception e) {
            status.append("Error getting service status: ").append(e.getMessage());
        }
        status.append("\n");
        status.append("ClassLoader stats: ").append(caller.getClassLoaderStats());

        return status.toString().replace("\n", "<br>");
    }
}

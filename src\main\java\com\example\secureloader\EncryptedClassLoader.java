
package com.example.secureloader;

import com.example.security.KeyProvider;
import com.example.security.DefaultKeyProvider;
import com.example.monitoring.PerformanceMonitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 自定义类加载器，支持加密类的透明解密加载
 *
 * 功能特性:
 * 1. 继承URLClassLoader，保持与Spring Boot的兼容性
 * 2. 智能路由：加密类走解密路径，普通类走默认路径
 * 3. 类缓存：已解密的类缓存在内存中，提升性能
 * 4. 多种密钥源：支持系统属性、环境变量等多种密钥获取方式
 * 5. 线程安全：使用ConcurrentHashMap保证并发安全
 */
@Slf4j
public class EncryptedClassLoader extends URLClassLoader {

    private static final String ALGORITHM = "AES";
    private static final String DEFAULT_KEY = "1234567890abcdef";
    private static final String ENCRYPTED_PACKAGE_PREFIX = "com.example.secure";
    private static final String KEY_PROPERTY = "decrypt.key";
    private static final String KEY_ENV = "DECRYPT_KEY";

    private final KeyProvider keyProvider;
    private final PerformanceMonitor performanceMonitor;
    private final Path encryptedDir;
    private final ConcurrentHashMap<String, Class<?>> classCache = new ConcurrentHashMap<>();
    private final boolean debugMode;

    public EncryptedClassLoader(URL[] urls, ClassLoader parent) {
        super(urls, parent);

        // 初始化调试模式
        this.debugMode = Boolean.parseBoolean(System.getProperty("encrypt.debug", "false"));

        // 初始化密钥提供者和性能监控
        this.keyProvider = new DefaultKeyProvider();
        this.performanceMonitor = new PerformanceMonitor();

        // 初始化加密文件目录
        this.encryptedDir = Paths.get("encrypted");

        if (debugMode) {
            log.debug("EncryptedClassLoader initialized:");
            log.debug("  - Encrypted dir: {}", encryptedDir.toAbsolutePath());
            log.debug("  - Key provider: {}", keyProvider.getClass().getSimpleName());
            log.debug("  - Debug mode: enabled");
        }
    }

    @Override
    protected Class<?> findClass(String name) throws ClassNotFoundException {
        long startTime = performanceMonitor.recordClassLoadStart(name);

        if (debugMode) {
            log.debug("Loading class: {}", name);
        }

        // 检查缓存
        Class<?> cachedClass = classCache.get(name);
        if (cachedClass != null) {
            if (debugMode) {
                log.debug("Class loaded from cache: {}", name);
            }
            performanceMonitor.recordEncryptedClassLoadComplete(name, startTime, true);
            return cachedClass;
        }

        // 判断是否为需要解密的类
        if (isEncryptedClass(name)) {
            try {
                Class<?> clazz = loadEncryptedClass(name);
                classCache.put(name, clazz);

                performanceMonitor.recordEncryptedClassLoadComplete(name, startTime, false);

                if (debugMode) {
                    long duration = System.currentTimeMillis() - startTime;
                    log.debug("Encrypted class loaded successfully: {} (took {}ms)", name, duration);
                }

                return clazz;
            } catch (Exception e) {
                performanceMonitor.recordClassLoadFailure(name, e);
                log.error("Failed to load encrypted class: {}", name, e);
                throw new ClassNotFoundException("Failed to load encrypted class: " + name, e);
            }
        }

        // 其他类使用默认加载方式
        try {
            Class<?> clazz = super.findClass(name);
            if (debugMode) {
                log.debug("Regular class loaded: {}", name);
            }
            return clazz;
        } catch (ClassNotFoundException e) {
            performanceMonitor.recordClassLoadFailure(name, e);
            if (debugMode) {
                log.debug("Class not found: {}", name);
            }
            throw e;
        }
    }

    /**
     * 判断是否为需要解密的类
     */
    private boolean isEncryptedClass(String className) {
        return className.startsWith(ENCRYPTED_PACKAGE_PREFIX);
    }

    /**
     * 加载并解密加密的类
     */
    private Class<?> loadEncryptedClass(String className) throws Exception {
        // 构造加密文件路径
        String classPath = className.replace('.', '/') + ".class";
        Path encryptedFile = encryptedDir.resolve(classPath);

        if (debugMode) {
            log.debug("Looking for encrypted file: {}", encryptedFile.toAbsolutePath());
        }

        if (!Files.exists(encryptedFile)) {
            throw new ClassNotFoundException("Encrypted class file not found: " + encryptedFile);
        }

        // 读取加密文件
        byte[] encryptedBytes = Files.readAllBytes(encryptedFile);

        if (debugMode) {
            log.debug("Read encrypted file: {} bytes", encryptedBytes.length);
        }

        // AES解密
        byte[] decryptedBytes = decrypt(encryptedBytes);

        if (debugMode) {
            log.debug("Decrypted to: {} bytes", decryptedBytes.length);
        }

        // 定义类
        return defineClass(className, decryptedBytes, 0, decryptedBytes.length);
    }

    /**
     * AES解密
     */
    private byte[] decrypt(byte[] encryptedData) throws Exception {
        // 从KeyProvider获取密钥
        String keyStr = keyProvider.getDecryptionKey();
        byte[] keyBytes = prepareKey(keyStr);

        Cipher cipher = Cipher.getInstance(ALGORITHM);
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        return cipher.doFinal(encryptedData);
    }

    /**
     * 获取解密密钥
     */
    private String getDecryptionKey() {
        // 优先级1: 系统属性
        String key = System.getProperty(KEY_PROPERTY);
        if (StringUtils.hasText(key)) {
            return key.trim();
        }

        // 优先级2: 环境变量
        key = System.getenv(KEY_ENV);
        if (StringUtils.hasText(key)) {
            return key.trim();
        }

        // 优先级3: 默认密钥
        if (debugMode) {
            System.out.println("WARNING: Using default decryption key (development only!)");
        }
        return DEFAULT_KEY;
    }

    /**
     * 获取密钥来源信息
     */
    private String getKeySource() {
        if (System.getProperty(KEY_PROPERTY) != null) {
            return "system property";
        }
        if (System.getenv(KEY_ENV) != null) {
            return "environment variable";
        }
        return "default key";
    }

    /**
     * 准备密钥字节数组
     */
    private byte[] prepareKey(String keyStr) {
        try {
            byte[] keyBytes = keyStr.getBytes("UTF-8");

            // AES密钥长度必须是16、24或32字节
            if (keyBytes.length == 16 || keyBytes.length == 24 || keyBytes.length == 32) {
                return keyBytes;
            }

            // 自动调整密钥长度到16字节
            byte[] adjustedKey = new byte[16];
            if (keyBytes.length < 16) {
                // 密钥太短，用0填充
                System.arraycopy(keyBytes, 0, adjustedKey, 0, keyBytes.length);
                if (debugMode) {
                    System.out.println("WARNING: Key too short, padded with zeros");
                }
            } else {
                // 密钥太长，截断到16字节
                System.arraycopy(keyBytes, 0, adjustedKey, 0, 16);
                if (debugMode) {
                    System.out.println("WARNING: Key too long, truncated to 16 bytes");
                }
            }

            return adjustedKey;
        } catch (Exception e) {
            throw new RuntimeException("Failed to prepare decryption key", e);
        }
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        return String.format("Class cache: %d classes loaded", classCache.size());
    }

    /**
     * 获取性能统计信息
     */
    public String getPerformanceStats() {
        return performanceMonitor.getDetailedStats();
    }

    /**
     * 清空类缓存
     */
    public void clearCache() {
        classCache.clear();
        if (debugMode) {
            log.debug("Class cache cleared");
        }
    }
}


package com.example.service;

import com.example.secureloader.EncryptedClassLoader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 安全逻辑调用服务
 *
 * 注意：这个类主要用于演示和测试目的
 * 在新的架构中，加密的类将通过Spring的自动扫描直接注入使用
 */
@Slf4j
@Service
public class SecureLogicCaller {

    /**
     * 运行安全逻辑 - 兼容旧版本的手动调用方式
     */
    public void runSecureLogic() {
        try {
            // 创建加密目录URL
            Path encryptedDir = Paths.get("encrypted");
            URL encryptedUrl = encryptedDir.toUri().toURL();
            URL[] urls = {encryptedUrl};

            // 创建自定义ClassLoader
            ClassLoader currentClassLoader = this.getClass().getClassLoader();
            EncryptedClassLoader loader = new EncryptedClassLoader(urls, currentClassLoader);

            // 动态加载加密的类
            Class<?> clazz = loader.loadClass("com.example.secure.SecretLogic");
            Object instance = clazz.getDeclaredConstructor().newInstance();
            clazz.getMethod("run").invoke(instance);

            log.info("Secure logic executed successfully via manual loading");

        } catch (Exception e) {
            log.error("Failed to execute secure logic: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取ClassLoader统计信息
     */
    public String getClassLoaderStats() {
        try {
            Path encryptedDir = Paths.get("encrypted");
            URL encryptedUrl = encryptedDir.toUri().toURL();
            URL[] urls = {encryptedUrl};

            ClassLoader currentClassLoader = this.getClass().getClassLoader();
            EncryptedClassLoader loader = new EncryptedClassLoader(urls, currentClassLoader);

            return loader.getCacheStats();
        } catch (Exception e) {
            log.error("Error getting cache stats: {}", e.getMessage(), e);
            return "Failed to get stats: " + e.getMessage();
        }
    }
}

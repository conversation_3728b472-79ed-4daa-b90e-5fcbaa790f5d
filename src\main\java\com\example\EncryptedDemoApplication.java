
package com.example;

import com.example.secureloader.EncryptedClassLoader;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;

import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Spring Boot 主启动类
 *
 * 集成自定义ClassLoader以支持加密类的透明加载
 *
 * 启动流程:
 * 1. 创建自定义EncryptedClassLoader
 * 2. 设置线程上下文ClassLoader
 * 3. 使用自定义ClassLoader启动Spring Boot
 */
@Slf4j
@SpringBootApplication
@MapperScan("com.example.secure.dao")
public class EncryptedDemoApplication {

    public static void main(String[] args) {
        try {
            log.info("=== Starting with Encrypted ClassLoader ===");

            // 1. 创建自定义ClassLoader
            Path encryptedDir = Paths.get("encrypted");
            URL[] urls = {}; // 空的URL数组，不添加加密目录到classpath

            log.info("Encrypted directory: {}", encryptedDir.toAbsolutePath());

            // 2. 创建自定义ClassLoader
            ClassLoader currentClassLoader = EncryptedDemoApplication.class.getClassLoader();
            EncryptedClassLoader encryptedLoader = new EncryptedClassLoader(urls, currentClassLoader);

            // 3. 设置线程上下文ClassLoader
            Thread.currentThread().setContextClassLoader(encryptedLoader);

            // 4. 使用自定义ClassLoader启动Spring Boot
            SpringApplication app = new SpringApplicationBuilder(EncryptedDemoApplication.class)
                    .build();

            log.info("=== Spring Boot Starting with Encrypted ClassLoader ===");
            app.run(args);

        } catch (Exception e) {
            log.error("Failed to start application with encrypted ClassLoader: {}", e.getMessage(), e);

            // 降级到普通模式
            log.warn("Falling back to normal mode...");
            SpringApplication.run(EncryptedDemoApplication.class, args);
        }
    }
}

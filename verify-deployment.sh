#!/bin/bash

echo "========================================"
echo "Spring Boot 加密应用部署验证脚本"
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 验证函数
check_file() {
    if [ -f "$1" ]; then
        echo -e "${GREEN}✅ $2${NC}"
        return 0
    else
        echo -e "${RED}❌ $2${NC}"
        return 1
    fi
}

check_dir() {
    if [ -d "$1" ]; then
        echo -e "${GREEN}✅ $2${NC}"
        return 0
    else
        echo -e "${RED}❌ $2${NC}"
        return 1
    fi
}

echo "1. 文件结构验证"
echo "----------------------------------------"

# 检查主JAR包
check_file "target/springboot-encrypted-demo.jar" "主JAR包存在"
if [ -f "target/springboot-encrypted-demo.jar" ]; then
    jar_size=$(du -h target/springboot-encrypted-demo.jar | cut -f1)
    echo -e "   ${BLUE}文件大小: $jar_size${NC}"
fi

# 检查加密文件目录
check_dir "encrypted" "加密文件根目录存在"
check_dir "encrypted/com/example/secure" "加密类文件目录存在"

# 检查具体的加密文件
echo ""
echo "2. 加密文件验证"
echo "----------------------------------------"

encrypted_files=(
    "encrypted/com/example/secure/SecretLogic.class"
    "encrypted/com/example/secure/AdvancedSecretService.class"
    "encrypted/com/example/secure/TransactionalSecretService.class"
)

total_files=0
encrypted_count=0

for file in "${encrypted_files[@]}"; do
    total_files=$((total_files + 1))
    if [ -f "$file" ]; then
        encrypted_count=$((encrypted_count + 1))
        file_size=$(du -h "$file" | cut -f1)
        echo -e "${GREEN}✅ $(basename "$file") - $file_size${NC}"
        
        # 验证文件是否真的被加密（不是标准Java class文件）
        if file "$file" | grep -q "Java class"; then
            echo -e "   ${YELLOW}⚠️  警告: 文件可能未加密（仍为Java class格式）${NC}"
        else
            echo -e "   ${GREEN}🔒 文件已加密（二进制数据）${NC}"
        fi
    else
        echo -e "${RED}❌ $(basename "$file") - 文件不存在${NC}"
    fi
done

echo ""
echo "3. JAR包内容验证"
echo "----------------------------------------"

# 检查JAR包是否排除了敏感类
if [ -f "target/springboot-encrypted-demo.jar" ]; then
    echo "检查JAR包是否正确排除了敏感类..."
    
    excluded_classes=(
        "com/example/secure/SecretLogic.class"
        "com/example/secure/AdvancedSecretService.class"
        "com/example/secure/TransactionalSecretService.class"
    )
    
    for class in "${excluded_classes[@]}"; do
        if jar -tf target/springboot-encrypted-demo.jar | grep -q "$class"; then
            echo -e "${RED}❌ 警告: JAR包中仍包含敏感类 $class${NC}"
        else
            echo -e "${GREEN}✅ 敏感类已从JAR包中排除: $class${NC}"
        fi
    done
    
    # 检查JAR包中是否包含必要的类
    essential_classes=(
        "com/example/EncryptedDemoApplication.class"
        "com/example/controller/TestController.class"
        "com/example/secureloader/EncryptedClassLoader.class"
    )
    
    echo ""
    echo "检查JAR包是否包含必要的类..."
    for class in "${essential_classes[@]}"; do
        if jar -tf target/springboot-encrypted-demo.jar | grep -q "$class"; then
            echo -e "${GREEN}✅ 必要类存在: $class${NC}"
        else
            echo -e "${RED}❌ 必要类缺失: $class${NC}"
        fi
    done
fi

echo ""
echo "4. 依赖验证"
echo "----------------------------------------"

# 检查Maven依赖
if [ -d "target/dependency" ]; then
    dep_count=$(find target/dependency -name "*.jar" | wc -l)
    echo -e "${GREEN}✅ Maven依赖已复制: $dep_count 个JAR文件${NC}"
    
    # 检查关键依赖
    key_deps=(
        "spring-boot-starter-web"
        "spring-boot-starter-aop"
        "spring-tx"
    )
    
    for dep in "${key_deps[@]}"; do
        if find target/dependency -name "*$dep*" | grep -q .; then
            echo -e "${GREEN}✅ 关键依赖存在: $dep${NC}"
        else
            echo -e "${RED}❌ 关键依赖缺失: $dep${NC}"
        fi
    done
else
    echo -e "${YELLOW}⚠️  依赖目录不存在，请执行: mvn dependency:copy-dependencies${NC}"
fi

echo ""
echo "5. 配置验证"
echo "----------------------------------------"

# 检查配置文件
config_files=(
    "src/main/resources/application.properties"
    "src/main/resources/application.yml"
)

config_found=false
for config in "${config_files[@]}"; do
    if [ -f "$config" ]; then
        echo -e "${GREEN}✅ 配置文件存在: $config${NC}"
        config_found=true
    fi
done

if [ "$config_found" = false ]; then
    echo -e "${YELLOW}⚠️  未找到Spring Boot配置文件${NC}"
fi

echo ""
echo "6. 总结报告"
echo "========================================"

echo -e "${BLUE}文件统计:${NC}"
echo "- 加密文件: $encrypted_count/$total_files"
echo "- 主JAR包: $([ -f "target/springboot-encrypted-demo.jar" ] && echo "存在" || echo "缺失")"
echo "- 依赖文件: $([ -d "target/dependency" ] && find target/dependency -name "*.jar" | wc -l || echo "0") 个"

echo ""
echo -e "${BLUE}部署状态:${NC}"
if [ $encrypted_count -eq $total_files ] && [ -f "target/springboot-encrypted-demo.jar" ]; then
    echo -e "${GREEN}🎉 部署验证通过！应用可以启动${NC}"
    echo ""
    echo -e "${BLUE}启动建议:${NC}"
    echo "1. 生产模式: java -jar target/springboot-encrypted-demo.jar"
    echo "2. 调试模式: java -Dencrypt.debug=true -jar target/springboot-encrypted-demo.jar"
    echo "3. 自定义密钥: java -Ddecrypt.key=your-key -jar target/springboot-encrypted-demo.jar"
    echo ""
    echo -e "${BLUE}验证接口:${NC}"
    echo "- 服务状态: curl http://localhost:8080/service-status"
    echo "- 加密功能: curl http://localhost:8080/run-secure-direct"
    echo "- 性能统计: curl http://localhost:8080/performance-stats"
else
    echo -e "${RED}❌ 部署验证失败！请检查上述错误${NC}"
    echo ""
    echo -e "${YELLOW}修复建议:${NC}"
    echo "1. 重新构建: mvn clean package"
    echo "2. 复制加密文件: cp -r target/encrypted encrypted/"
    echo "3. 复制依赖: mvn dependency:copy-dependencies"
fi

echo ""
echo "========================================"

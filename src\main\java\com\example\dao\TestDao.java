package com.example.dao;

import com.example.secure.entity.TestEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Test表数据访问接口
 * 注意：此接口不加密，以确保MyBatis能够正常创建代理实现
 */
@Mapper
public interface TestDao {
    
    /**
     * 根据ID查询记录
     * @param id 主键ID
     * @return 查询结果
     */
    TestEntity selectById(@Param("id") Integer id);
    
    /**
     * 查询所有记录
     * @return 所有记录列表
     */
    List<TestEntity> selectAll();
    
    /**
     * 根据用户地址查询记录
     * @param userAddress 用户地址
     * @return 查询结果列表
     */
    List<TestEntity> selectByUserAddress(@Param("userAddress") String userAddress);
    
    /**
     * 插入新记录
     * @param entity 要插入的实体
     * @return 影响的行数
     */
    int insert(TestEntity entity);
    
    /**
     * 更新记录
     * @param entity 要更新的实体
     * @return 影响的行数
     */
    int update(TestEntity entity);
    
    /**
     * 根据ID删除记录（逻辑删除）
     * @param id 主键ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Integer id);
}

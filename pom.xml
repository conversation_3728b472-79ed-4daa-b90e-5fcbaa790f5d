<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.example</groupId>
    <artifactId>springboot-encrypted-demo</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>Spring Boot Encrypted Demo</name>
    <description>Spring Boot with encrypted class loader</description>

    <properties>
        <java.version>1.8</java.version>
        <spring.boot.version>2.6.4</spring.boot.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>

        <!-- Spring Boot AOP 依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>

        <!-- Spring Boot 事务依赖 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>5.3.16</version>
        </dependency>

        <!-- Spring Boot JDBC 依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>

        <!-- MyBatis Spring Boot Starter -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.2.2</version>
        </dependency>

        <!-- MySQL Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
        </dependency>

        <!-- Lombok 依赖 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.26</version>
            <scope>provided</scope>
        </dependency>

        <!-- JUnit 5 测试依赖 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.9.2</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>Nexus Repository</name>
            <url>http://gitlab.53site.com:8081/repository/server-releases/</url>
        </repository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>repository.jboss.org-public</id>
            <name>JBoss.org Maven repository</name>
            <url>https://repository.jboss.org/nexus/content/groups/public</url>
        </repository>
    </repositories>

    <build>
        <finalName>${artifactId}</finalName>
        <plugins>
            <!-- Maven编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <!-- 复制需要加密的类文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>copy-secure-classes</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <echo message="Copying secure classes for encryption..."/>
                                <mkdir dir="${project.build.directory}/plain"/>
                                <copy todir="${project.build.directory}/plain" preservelastmodified="true">
                                    <fileset dir="${project.build.outputDirectory}">
                                        <include name="**/secure/**/*.class"/>
                                    </fileset>
                                </copy>
                                <echo message="Copied ${toString:copied.files} files"/>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 执行类文件加密 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>encrypt-classes</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>java</goal>
                        </goals>
                        <configuration>
                            <mainClass>com.example.build.EncryptTool</mainClass>
                            <arguments>
                                <argument>${project.build.directory}/plain</argument>
                                <argument>${project.build.directory}/encrypted</argument>
                            </arguments>
                            <systemProperties>
                                <systemProperty>
                                    <key>encrypt.key</key>
                                    <value>${encrypt.key}</value>
                                </systemProperty>
                            </systemProperties>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 从JAR中排除原始的敏感类文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <excludes>
                        <exclude>**/secure/**/*.class</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- ProGuard 插件 - 已禁用，方便测试加密类加载 -->
            <!--
            <plugin>
                <groupId>com.github.wvengen</groupId>
                <artifactId>proguard-maven-plugin</artifactId>
                <version>2.7.0</version>
                <executions>
                    <execution>
                        <id>proguard</id>
                        <phase>package</phase>
                        <goals>
                            <goal>proguard</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <obfuscate>true</obfuscate>
                    <injar>${project.build.finalName}.jar</injar>
                    <outjar>${project.build.finalName}.jar</outjar>
                    <outputDirectory>${project.build.directory}</outputDirectory>
                    <proguardInclude>${basedir}/proguard.conf</proguardInclude>
                    <options>
                        <option>-dontwarn</option>
                        <option>-dontshrink</option>
                        <option>-dontoptimize</option>
                        <option>-keepattributes Exceptions,InnerClasses,Signature,Deprecated,
                            SourceFile,LineNumberTable, *Annotation*,EnclosingMethod
                        </option>
                        <option>-adaptclassstrings</option>
                        <option>
                            -keep class com.example.EncryptedDemoApplication { *; }
                        </option>
                        <option>
                            -keep class com.example.secure.** { *; }
                        </option>
                        <option>
                            -keep class com.example.secureloader.** { *; }
                        </option>
                        <option>
                            -keep class com.example.security.** { *; }
                        </option>
                        <option>
                            -keep class com.example.aop.** { *; }
                        </option>
                        <option>
                            -keep class com.example.controller.** { *; }
                        </option>
                        <option>-keepnames interface ** { *; }</option>
                        <option>
                            -keep class * {
                            @org.springframework.web.bind.annotation.GetMapping *;
                            @org.springframework.web.bind.annotation.RestController *;
                            @org.springframework.beans.factory.annotation.Autowired *;
                            @org.springframework.beans.factory.annotation.Value *;
                            @org.springframework.stereotype.Service *;
                            @org.springframework.stereotype.Component *;
                            @org.springframework.scheduling.annotation.Scheduled *;
                            }
                        </option>
                    </options>
                    <libs>
                        <lib>${java.home}/lib/jrt-fs.jar</lib>
                    </libs>
                </configuration>
            </plugin>
            -->


            <!-- Spring Boot 打包插件 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <mainClass>com.example.EncryptedDemoApplication</mainClass>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>

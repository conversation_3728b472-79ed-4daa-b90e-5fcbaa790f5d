# 技术方案设计

## 系统架构概览

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    构建期 (Build Time)                      │
├─────────────────────────────────────────────────────────────┤
│  Maven Build Process                                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Compile   │→ │   Extract   │→ │   Encrypt   │         │
│  │   Classes   │  │   Target    │  │   Classes   │         │
│  │             │  │   Classes   │  │             │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│                                           │                 │
│  ┌─────────────┐  ┌─────────────┐         ▼                 │
│  │   Package   │← │   Exclude   │  ┌─────────────┐         │
│  │     JAR     │  │   Original  │  │   Output    │         │
│  │             │  │   Classes   │  │  Encrypted  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│                    运行期 (Runtime)                         │
├─────────────────────────────────────────────────────────────┤
│  Spring Boot Application                                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Custom    │→ │   Decrypt   │→ │   Define    │         │
│  │ ClassLoader │  │   Classes   │  │   Classes   │         │
│  │             │  │             │  │             │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│                                           │                 │
│  ┌─────────────┐  ┌─────────────┐         ▼                 │
│  │   Spring    │← │   Component │  ┌─────────────┐         │
│  │   Context   │  │    Scan     │  │   Normal    │         │
│  │             │  │             │  │   Spring    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 技术栈选型

### 核心技术栈

| 技术组件 | 版本 | 用途 | 选型理由 |
|---------|------|------|----------|
| Spring Boot | 2.6.4 | 应用框架 | 项目现有技术栈，成熟稳定 |
| Spring AOP | 2.6.4 | 切面编程 | 支持加密类的AOP功能 |
| Maven | 3.6+ | 构建工具 | 项目现有构建工具 |
| AES加密 | JDK内置 | 字节码加密 | 性能好，安全性高 |
| Custom ClassLoader | JDK内置 | 类加载 | 灵活可控，与Spring兼容 |
| ProGuard | 2.7.0 | 代码混淆 | 额外的代码保护层 |

### Maven插件选型

| 插件 | 版本 | 阶段 | 功能 |
|------|------|------|------|
| maven-compiler-plugin | 3.11.0 | compile | 编译Java源码 |
| maven-antrun-plugin | 3.1.0 | process-classes | 复制目标类文件 |
| exec-maven-plugin | 3.1.0 | process-classes | 执行EncryptTool加密 |
| maven-jar-plugin | 3.3.0 | package | 排除原始类文件 |
| maven-dependency-plugin | 3.2.0 | package | 复制依赖JAR包 |
| proguard-maven-plugin | 2.7.0 | package | 代码混淆（可选） |

### 核心组件架构

| 组件 | 类名 | 功能 | 特性 |
|------|------|------|------|
| 加密工具 | EncryptTool | 构建期类文件加密 | 批量加密、多密钥源、验证功能 |
| 类加载器 | EncryptedClassLoader | 运行期透明解密 | 智能路由、缓存机制、性能监控 |
| 密钥管理 | KeyProvider/DefaultKeyProvider | 密钥获取和管理 | 多源获取、轮换支持、验证机制 |
| 性能监控 | PerformanceMonitor | 性能统计和监控 | 详细统计、缓存监控、性能警告 |
| Bean注册 | EncryptedBeanRegistrar | 加密类Spring注册 | 自动注册、开发模式支持 |
| AOP切面 | SecretLogicAspect | 加密类AOP支持 | 方法拦截、性能统计、日志记录 |

## 详细设计

### 1. 构建期加密设计

#### 1.1 Maven构建流程

```xml
<build>
    <plugins>
        <!-- 阶段1: 编译所有Java源码 -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>3.11.0</version>
            <configuration>
                <source>11</source>
                <target>11</target>
            </configuration>
        </plugin>

        <!-- 阶段2: 复制需要加密的类文件 -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <version>3.1.0</version>
            <executions>
                <execution>
                    <id>copy-secure-classes</id>
                    <phase>process-classes</phase>
                    <goals><goal>run</goal></goals>
                    <configuration>
                        <target>
                            <copy todir="${project.build.directory}/plain">
                                <fileset dir="${project.build.outputDirectory}">
                                    <include name="**/secure/**/*.class"/>
                                </fileset>
                            </copy>
                        </target>
                    </configuration>
                </execution>
            </executions>
        </plugin>

        <!-- 阶段3: 执行加密 -->
        <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>exec-maven-plugin</artifactId>
            <version>3.1.0</version>
            <executions>
                <execution>
                    <id>encrypt-classes</id>
                    <phase>process-classes</phase>
                    <goals><goal>java</goal></goals>
                    <configuration>
                        <mainClass>com.example.build.EncryptTool</mainClass>
                        <arguments>
                            <argument>${project.build.directory}/plain</argument>
                            <argument>${project.build.directory}/encrypted</argument>
                        </arguments>
                    </configuration>
                </execution>
            </executions>
        </plugin>

        <!-- 阶段4: 排除原始类文件 -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jar-plugin</artifactId>
            <version>3.3.0</version>
            <configuration>
                <excludes>
                    <exclude>**/secure/**/*.class</exclude>
                </excludes>
            </configuration>
        </plugin>
    </plugins>
</build>
```

#### 1.2 加密工具设计（EncryptTool.java）

**核心特性：**
- 批量加密：递归处理目录中的所有.class文件
- 多密钥源：系统属性 → 环境变量 → 默认密钥
- 自动密钥调整：支持16/24/32字节密钥，自动填充或截断
- 验证功能：内置加密解密验证机制
- 详细日志：完整的加密过程和错误信息

**使用方式：**
```bash
# Maven构建中自动调用
mvn clean package -Dencrypt.key=your-secret-key

# 手动调用
java com.example.build.EncryptTool target/plain target/encrypted
```

**密钥获取优先级：**
```java
// 1. 系统属性
-Dencrypt.key=your-secret-key

// 2. 环境变量
export ENCRYPT_KEY=your-secret-key

// 3. 默认密钥（仅开发环境）
"1234567890abcdef"
```

**加密流程：**
```
输入目录 → 扫描.class文件 → AES加密 → 保持目录结构 → 输出到目标目录
```

**验证机制：**
- 输入参数验证
- 密钥格式验证
- 加密解密一致性验证
- 文件完整性检查

### 2. 运行期解密设计

#### 2.1 自定义ClassLoader（EncryptedClassLoader.java）

**核心特性：**
- 智能路由：加密类走解密路径，普通类走默认路径
- 性能缓存：ConcurrentHashMap实现的线程安全类缓存
- 调试支持：详细的调试日志和性能统计
- 错误处理：完善的异常处理和降级机制
- 密钥管理：集成KeyProvider接口，支持多种密钥源

**关键组件集成：**
```java
public class EncryptedClassLoader extends URLClassLoader {
    private final KeyProvider keyProvider;           // 密钥提供者
    private final PerformanceMonitor performanceMonitor;  // 性能监控
    private final ConcurrentHashMap<String, Class<?>> classCache;  // 类缓存
    private final boolean debugMode;                 // 调试模式
}
```

**类加载流程：**
```
1. 检查类名是否为加密类（com.example.secure前缀）
2. 查找缓存中是否已存在
3. 如果是加密类：
   - 从encrypted/目录读取加密文件
   - 使用KeyProvider获取密钥
   - AES解密字节码
   - defineClass()创建Class对象
   - 缓存到内存中
4. 如果是普通类：使用默认类加载机制
```

**性能优化：**
- 类缓存：避免重复解密
- 延迟加载：只在需要时才解密
- 并发安全：ConcurrentHashMap保证线程安全
- 性能监控：记录加载时间和缓存命中率

**调试功能：**
```bash
# 启用调试模式
java -Dencrypt.debug=true -jar app.jar

# 输出示例：
# Loading class: com.example.secure.SecretLogic
# Encrypted class loaded successfully: com.example.secure.SecretLogic (took 45ms)
# Class loaded from cache: com.example.secure.SecretLogic
```

#### 2.2 Spring Boot集成

```java
package com.example;

import com.example.secureloader.EncryptedClassLoader;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.core.io.DefaultResourceLoader;

import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;

@SpringBootApplication
public class EncryptedDemoApplication {

    public static void main(String[] args) throws Exception {
        // 1. 设置加密目录到classpath
        Path encryptedDir = Paths.get("encrypted");
        URL encryptedUrl = encryptedDir.toUri().toURL();
        URL[] urls = {encryptedUrl};

        // 2. 创建自定义ClassLoader
        ClassLoader currentClassLoader = EncryptedDemoApplication.class.getClassLoader();
        EncryptedClassLoader encryptedLoader = new EncryptedClassLoader(urls, currentClassLoader);

        // 3. 设置线程上下文ClassLoader
        Thread.currentThread().setContextClassLoader(encryptedLoader);

        // 4. 使用自定义ClassLoader启动Spring Boot
        SpringApplication app = new SpringApplicationBuilder(EncryptedDemoApplication.class)
                .resourceLoader(new DefaultResourceLoader(encryptedLoader))
                .build();
        
        app.run(args);
    }
}
```

## 数据库设计

本方案不涉及数据库设计，主要是文件系统和内存操作。

## 接口设计

### 内部接口

#### 1. 密钥提供接口

```java
public interface KeyProvider {
    String getDecryptionKey();
    void refreshKey();
    boolean isKeyValid(String key);
}
```

#### 2. 类加载监听接口

```java
public interface ClassLoadListener {
    void onClassLoaded(String className, boolean encrypted);
    void onClassLoadFailed(String className, Exception e);
}
```

### 配置接口

```java
@ConfigurationProperties(prefix = "encryption")
public class EncryptionProperties {
    private String keySource = "default";
    private String encryptedDir = "encrypted";
    private boolean debugMode = false;
    private String[] encryptedPackages = {"com.example.secure"};
    
    // getters and setters
}
```

## 安全设计

### 1. 密钥管理策略

```
优先级顺序:
1. JVM系统属性: -Ddecrypt.key=xxx
2. 环境变量: DECRYPT_KEY=xxx  
3. 外部配置文件: application.yml
4. 远程密钥服务: KMS/Vault
5. 默认密钥 (仅开发环境)
```

### 2. 安全加固措施

- **密钥轮换**: 支持运行时密钥更新
- **完整性校验**: 加密文件添加校验和
- **访问控制**: 限制加密文件的文件系统权限
- **审计日志**: 记录所有密钥访问和类加载操作

## 性能设计

### 1. 性能优化策略

- **类缓存**: 已解密的类缓存在内存中
- **延迟加载**: 只在需要时才解密类
- **并发安全**: 使用ConcurrentHashMap保证线程安全
- **资源管理**: 及时释放加密文件句柄

### 2. 性能指标

- **启动时间**: 增加不超过500ms
- **内存占用**: 增加不超过50MB
- **类加载时间**: 加密类加载时间不超过普通类的3倍

## 监控设计

### 1. 关键指标

- 加密类加载成功/失败次数
- 解密操作耗时统计
- 密钥访问次数和来源
- 缓存命中率

### 2. 日志设计

```java
// INFO级别 - 关键操作
log.info("Encrypted class loaded successfully: {}", className);

// WARN级别 - 性能警告  
log.warn("Class decryption took {}ms, exceeding threshold", duration);

// ERROR级别 - 错误情况
log.error("Failed to decrypt class: {}", className, exception);

// DEBUG级别 - 详细信息
log.debug("Loading encrypted class from: {}", encryptedFile);
```

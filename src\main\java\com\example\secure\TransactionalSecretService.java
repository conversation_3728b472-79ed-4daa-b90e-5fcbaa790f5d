package com.example.secure;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 事务性敏感服务 - 测试加密类的事务支持
 *
 * 这个加密类演示了：
 * 1. @Transactional注解的正常工作
 * 2. 事务回滚机制
 * 3. 事务传播行为
 */
@Slf4j
@Service
public class TransactionalSecretService {
    
    @Autowired
    private SecretLogic secretLogic;
    
    @Autowired
    private AdvancedSecretService advancedSecretService;
    
    /**
     * 事务性方法 - 正常执行
     */
    @Transactional
    public String executeTransactionalOperation(String data) {
        log.info("🔄 [TRANSACTION] Starting transactional operation");
        
        try {
            // 执行一系列操作
            String config = secretLogic.getSecretConfig();
            String calculation = secretLogic.performSecretCalculation();
            String processed = secretLogic.processSensitiveData(data);
            
            // 模拟数据库操作
            simulateDataOperation("INSERT", data);
            simulateDataOperation("UPDATE", processed);
            
            String result = String.format("Transactional operation completed: config=%s, calc=%s, processed=%s", 
                                        config, calculation, processed);
            
            log.info("✅ [TRANSACTION] Operation completed successfully");
            return result;
            
        } catch (Exception e) {
            log.error("❌ [TRANSACTION] Operation failed: {}", e.getMessage(), e);
            throw e; // 触发事务回滚
        }
    }
    
    /**
     * 事务性方法 - 模拟异常回滚
     */
    @Transactional
    public String executeTransactionalOperationWithRollback(String data, boolean shouldFail) {
        log.info("🔄 [TRANSACTION-ROLLBACK] Starting transactional operation (shouldFail={})", shouldFail);
        
        try {
            // 执行一些操作
            String processed = secretLogic.processSensitiveData(data);
            simulateDataOperation("INSERT", processed);
            
            if (shouldFail) {
                log.warn("💥 [TRANSACTION-ROLLBACK] Simulating failure...");
                throw new RuntimeException("Simulated transaction failure");
            }
            
            simulateDataOperation("UPDATE", processed + "_FINAL");
            
            log.info("✅ [TRANSACTION-ROLLBACK] Operation completed successfully");
            return "Transaction completed: " + processed;
            
        } catch (Exception e) {
            log.warn("🔙 [TRANSACTION-ROLLBACK] Transaction will be rolled back: {}", e.getMessage());
            throw e;
        }
    }
    
    /**
     * 嵌套事务方法
     */
    @Transactional
    public String executeNestedTransactionalOperation(String data) {
        log.info("🔄 [NESTED-TRANSACTION] Starting nested transactional operation");
        
        try {
            // 调用其他事务性方法
            String result1 = executeTransactionalOperation(data + "_PART1");
            
            // 调用高级服务
            String result2 = advancedSecretService.executeAdvancedOperation(data + "_PART2");
            
            // 模拟更多数据操作
            simulateDataOperation("BATCH_INSERT", data);
            
            String finalResult = "Nested transaction result: " + result1 + " | " + result2;
            log.info("✅ [NESTED-TRANSACTION] Nested operation completed");
            
            return finalResult;
            
        } catch (Exception e) {
            log.error("❌ [NESTED-TRANSACTION] Nested operation failed: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 只读事务方法
     */
    @Transactional(readOnly = true)
    public String executeReadOnlyOperation(String query) {
        log.info("📖 [READ-ONLY-TRANSACTION] Starting read-only operation");
        
        try {
            // 只读操作
            String config = secretLogic.getSecretConfig();
            boolean permission = secretLogic.validateUserPermission("readonly_user", "READ_" + query);
            
            // 模拟数据查询
            String queryResult = simulateDataQuery("SELECT", query);
            
            String result = String.format("Read-only result: config=%s, permission=%s, query=%s", 
                                        config, permission, queryResult);
            
            log.info("✅ [READ-ONLY-TRANSACTION] Read-only operation completed");
            return result;
            
        } catch (Exception e) {
            log.error("❌ [READ-ONLY-TRANSACTION] Read-only operation failed: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 模拟数据库操作
     */
    private void simulateDataOperation(String operation, String data) {
        log.debug("💾 [DB-OPERATION] {}: {}", operation, data);
        
        // 模拟数据库操作延迟
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.debug("✅ [DB-OPERATION] {} completed", operation);
    }
    
    /**
     * 模拟数据库查询
     */
    private String simulateDataQuery(String operation, String query) {
        System.out.println("🔍 [DB-QUERY] " + operation + ": " + query);
        
        // 模拟查询延迟
        try {
            Thread.sleep(5);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        String result = "QUERY_RESULT_" + query.toUpperCase() + "_" + System.currentTimeMillis() % 1000;
        System.out.println("📋 [DB-QUERY] " + operation + " result: " + result);
        
        return result;
    }
}
